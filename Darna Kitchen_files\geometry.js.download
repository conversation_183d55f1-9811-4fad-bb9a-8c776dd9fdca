google.maps.__gjsload__('geometry', function(_){var Dla=function(a,b){return Math.abs(_.Hk(b-a,-180,180))},Ela=function(a,b,c,d,e){if(!d){c=Dla(a.lng(),c)/Dla(a.lng(),b.lng());if(!e)return e=Math.sin(_.Ij(a.lat())),e=Math.log((1+e)/(1-e))/2,b=Math.sin(_.Ij(b.lat())),_.Jj(2*Math.atan(Math.exp(e+c*(Math.log((1+b)/(1-b))/2-e)))-Math.PI/2);a=e.fromLatLngToPoint(a);b=e.fromLatLngToPoint(b);return e.fromPointToLatLng(new _.cn(a.x+c*(b.x-a.x),a.y+c*(b.y-a.y))).lat()}e=_.Ij(a.lat());a=_.Ij(a.lng());d=_.Ij(b.lat());b=_.Ij(b.lng());c=_.Ij(c);return _.Hk(_.Jj(Math.atan2(Math.sin(e)*
Math.cos(d)*Math.sin(c-b)-Math.sin(d)*Math.cos(e)*Math.sin(c-a),Math.cos(e)*Math.cos(d)*Math.sin(a-b))),-90,90)},Fla=function(a,b){a=new _.xl(a,!1);b=new _.xl(b,!1);return a.equals(b)},Gla=function(a,b,c){a=_.Kl(a);c=c||1E-9;const d=_.Hk(a.lng(),-180,180),e=b instanceof _.iv,f=!!b.get("geodesic"),g=b.get("latLngs");b=b.get("map");b=!f&&b?b.getProjection():null;for(let r=0,u=g.getLength();r<u;++r){const w=g.getAt(r),x=w.getLength(),y=e?x:x-1;for(let D=0;D<y;++D){var h=w.getAt(D);const I=w.getAt((D+
1)%x);if(Fla(h,a)||Fla(I,a))return!0;var l=_.Hk(h.lng(),-180,180),n=_.Hk(I.lng(),-180,180);const L=Math.max(l,n),K=Math.min(l,n);if(l=Math.abs(_.Hk(l-n,-180,180))<=1E-9&&(Math.abs(_.Hk(l-d,-180,180))<=c||Math.abs(_.Hk(n-d,-180,180))<=c)){l=a.lat();n=Math.min(h.lat(),I.lat())-c;var p=Math.max(h.lat(),I.lat())+c;l=l>=n&&l<=p}if(l)return!0;if(L-K>180?d+c>=L||d-c<=K:d+c>=K&&d-c<=L)if(h=Ela(h,I,d,f,b),Math.abs(h-a.lat())<c)return!0}}return!1},kE=class{};kE.isLocationOnEdge=Gla;
kE.containsLocation=function(a,b){a=_.Kl(a);const c=_.Hk(a.lng(),-180,180),d=!!b.get("geodesic"),e=b.get("latLngs");var f=b.get("map");f=!d&&f?f.getProjection():null;let g=!1;for(let l=0,n=e.getLength();l<n;++l){const p=e.getAt(l);for(let r=0,u=p.getLength();r<u;++r){const w=p.getAt(r),x=p.getAt((r+1)%u);var h=_.Hk(w.lng(),-180,180);const y=_.Hk(x.lng(),-180,180),D=Math.max(h,y);h=Math.min(h,y);(D-h>180?c>=D||c<h:c<D&&c>=h)&&Ela(w,x,c,d,f)<a.lat()&&(g=!g)}}return g||Gla(a,b)};var Hla={encoding:_.Jv,spherical:_.Ju,poly:kE};_.pa.google.maps.geometry=Hla;_.jk("geometry",Hla);});
