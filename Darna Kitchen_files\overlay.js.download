google.maps.__gjsload__('overlay', function(_){var uua=function(){},$H=function(a){a.DB=a.DB||new uua;return a.DB},vua=function(a){this.Hk();this.Dg=new _.Ap(()=>{const b=a.DB;if(a.getPanes()){if(a.getProjection()){if(!b.Fg&&a.onAdd)a.onAdd();b.Fg=!0;a.draw()}}else{if(b.Fg)if(a.onRemove)a.onRemove();else a.remove();b.Fg=!1}},0)},xua=function(a,b){const c=$H(a);let d=c.Eg;d||(d=c.Eg=new vua(a));_.Eb(c.Dg||[],_.Wl);var e=c.Gg=c.Gg||new _.Bla;const f=b.__gm;e.bindTo("zoom",f);e.bindTo("offset",f);e.bindTo("center",f,"projectionCenterQ");e.bindTo("projection",
b);e.bindTo("projectionTopLeft",f);e=c.Ig=c.Ig||new wua(e);e.bindTo("zoom",f);e.bindTo("offset",f);e.bindTo("projection",b);e.bindTo("projectionTopLeft",f);a.bindTo("projection",e,"outProjection");a.bindTo("panes",f);e=()=>_.Bp(d.Dg);c.Dg=[_.Ul(a,"panes_changed",e),_.Ul(f,"zoom_changed",e),_.Ul(f,"offset_changed",e),_.Ul(b,"projection_changed",e),_.Ul(f,"projectioncenterq_changed",e)];_.Bp(d.Dg);b instanceof _.Cm?(_.Um(b,"Ox"),_.M(b,148440)):b instanceof _.vn&&(_.Um(b,"Oxs"),_.M(b,181451))},yua=function(a){const b=
$H(a);var c=b.Gg;c&&c.unbindAll();(c=b.Ig)&&c.unbindAll();a.unbindAll();a.set("panes",null);a.set("projection",null);b.Dg&&b.Dg.forEach(d=>{_.Wl(d)});b.Dg=null;b.Eg&&(_.Cp(b.Eg.Dg),b.Eg=null)},Dua=function(a){if(a){var b=a.getMap();if(zua(a)!==b&&b&&b instanceof _.Cm){const c=b.__gm;c.overlayLayer?a.__gmop=new Aua(b,a,c.overlayLayer):c.Eg.then(({Yg:d})=>{const e=new Bua(b,d);d.Oi(e);c.overlayLayer=e;Cua(a);Dua(a)})}}},Cua=function(a){if(a){var b=a.__gmop;b&&(a.__gmop=null,b.overlay.unbindAll(),b.overlay.set("panes",
null),b.overlay.set("projection",null),b.overlayLayer.Zn(b),b.Dg&&(b.Dg=!1,b.overlay.onRemove?b.overlay.onRemove():b.overlay.remove()))}},zua=function(a){return(a=a.__gmop)?a.map:null},Eua=function(a,b){a.overlay.get("projection")!==b&&(a.overlay.bindTo("panes",a.map.__gm),a.overlay.set("projection",b))},wua=class extends _.mm{constructor(a){super();this.projection=a}changed(a){a!=="outProjection"&&(a=!!(this.get("offset")&&this.get("projectionTopLeft")&&this.get("projection")&&_.Jk(this.get("zoom"))),
a===!this.get("outProjection")&&this.set("outProjection",a?this.projection:null))}};_.Ha(vua,_.mm);var Aua=class{constructor(a,b,c){this.map=a;this.overlay=b;this.overlayLayer=c;this.Dg=!1;_.Um(this.map,"Ox");_.M(this.map,148440);c.Cn(this)}draw(){this.Dg||(this.Dg=!0,this.overlay.onAdd&&this.overlay.onAdd());this.overlay.draw&&this.overlay.draw()}},Bua=class{constructor(a,b){this.map=a;this.Yg=b;this.Dg=null;this.Eg=[]}dispose(){}Jh(a,b,c,d,e,f,g,h){const l=this.Dg=this.Dg||new _.tD(this.map,this.Yg,()=>{});l.Jh(a,b,c,d,e,f,g,h);for(const n of this.Eg)Eua(n,l),n.draw()}Cn(a){this.Eg.push(a);this.Dg&&
Eua(a,this.Dg);this.Yg.refresh()}Zn(a){_.Kb(this.Eg,a)}};_.jk("overlay",{wD:function(a){if(a){yua(a);delete $H(a).Hg;Cua(a);var b=a.getMap();b&&(b instanceof _.Cm?Dua(a):a&&(b=a.getMap(),($H(a).Hg||null)!==b&&(b&&xua(a,b),$H(a).Hg=b)))}},preventMapHitsFrom:a=>{_.Jz(a,{Ul:({event:b})=>{_.ty(b.Dg)},Ck:b=>{_.uz(b)},Hq:b=>{_.vz(b)},zl:b=>{_.vz(b)},Ok:b=>{_.wz(b)}}).Uq(!0)},preventMapHitsAndGesturesFrom:a=>{a.addEventListener("click",_.Sl);a.addEventListener("contextmenu",_.Sl);a.addEventListener("dblclick",_.Sl);a.addEventListener("mousedown",_.Sl);a.addEventListener("mousemove",
_.Sl);a.addEventListener("MSPointerDown",_.Sl);a.addEventListener("pointerdown",_.Sl);a.addEventListener("touchstart",_.Sl);a.addEventListener("wheel",_.Sl)}});});
