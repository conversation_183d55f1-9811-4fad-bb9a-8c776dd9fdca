google.maps.__gjsload__('search', function(_){var Wqa=function(a){const b=[];a.data.forEach(c=>{b.push(...Vqa(c))});return b},Vqa=function(a){let b=_.yn,c=-1;a.tiles.forEach(e=>{e.zoom>c&&(b=e.si,c=e.zoom)});if(c===-1)return[];const d=[];a.gv().forEach(e=>{e.a&&e.a.length>=2&&d.push(new Xqa(e,b,c))});return d},Yqa=class extends _.mm{};var Zqa={["1"]:{}},Xqa=class{constructor(a,b,c){this.yn=b;this.zoom=c;this.bounds=this.anchor=null;this.Dg=Zqa;this.source=a;this.featureId=this.source.id||"0";this.infoWindowOffset=(this.source.io||[]).length===2?new google.maps.Point(this.source.io[0],this.source.io[1]):null}getAnchor(){if(!this.anchor){const a=1<<this.zoom;this.anchor=_.Lo(new _.Vo((this.yn.x*256+this.source.a[0])/a,(this.yn.y*256+this.source.a[1])/a)).toJSON()}return this.anchor}getCompleteBounds(){return this.getBounds().reduce((a,
b)=>{a.extendByBounds(b);return a},_.Mn(0,0,0,0))}getBounds(){if(this.bounds===null){this.bounds=[];const a=this.source.bb||[];if(a.length%4===0)for(let b=0;b<a.length;b+=4){const c=this.bounds[this.bounds.length-1],d=_.Mn(a[b],a[b+1],a[b+2],a[b+3]);c&&c.equals(d)||this.bounds.push(d)}}return[...this.bounds]}getExtendedContent(a){if(this.Dg===Zqa)try{this.Dg=this.source.c?JSON.parse(this.source.c):{}}catch(b){this.Dg={}}return this.Dg[a]??{}}getFeatureName(){return this.getExtendedContent("1")?.title??
null}isTransitStation(){return this.getExtendedContent("1")?.is_transit_station??!1}};var $qa=new WeakSet,qH=class extends Yqa{constructor(a){super();this.setValues(a);this.setOptions=b=>{this.setValues(b)};_.ik("search_impl")}changed(){const a=this;var b=this.get("map");let c=null;b&&(c=b.__gm,b=Number(c.get("blockingLayerCount"))||0,c.set("blockingLayerCount",b+1),c.set("disableLabelingHysteresis",this.get("disableLabelingHysteresis")),c.set("tilePrefetchEnabled",this.get("tilePrefetchEnabled")));_.ik("search_impl").then(d=>{d.Dg(a);c&&(d=Number(c.get("blockingLayerCount"))||0,c.set("blockingLayerCount",
d-1))})}static enableFeatureMapEventsRasterOnly(a){if(_.Rp[15]){var b=a.__gm.Xg;if(!$qa.has(a)){$qa.add(a);var c=[],d=(f,g)=>{f=Vqa(f);f.length&&_.im(a,g,f)},e=()=>{for(;c.length>0;)c.pop().remove();b.forEach(f=>{if(f=f.data)c.push(_.Ul(f,"insert",g=>{d(g,"addfeatures")})),c.push(_.Ul(f,"remove",g=>{d(g,"removefeatures")}))})};b.addListener("insert_at",e);b.addListener("remove_at",e);b.addListener("set_at",e);e()}(()=>{const f=[];b.forEach(g=>{f.push(...Wqa(g))});f.length&&_.im(a,"addfeatures",f)})()}}};
qH.enableFeatureMapEventsRasterOnly=qH.enableFeatureMapEventsRasterOnly;_.Pm(qH.prototype,{map:_.Zs});_.pa.google.maps.search={GoogleLayer:qH};_.jk("search",{});});
